/**
 * 脚本验证工具
 * 检查所有 K6 测试脚本的语法和基本功能
 */

const fs = require('fs');
const path = require('path');

// 要验证的脚本文件
const SCRIPT_FILES = [
    'arbitrum_issue_reproduce.js',
    'arbitrum_blockpi_test.js',
    'arbitrum_advanced_test.js'
];

// 验证结果
let validationResults = {
    passed: 0,
    failed: 0,
    errors: []
};

/**
 * 验证单个脚本文件
 */
function validateScript(filename) {
    console.log(`\n🔍 验证脚本: ${filename}`);
    
    try {
        // 检查文件是否存在
        if (!fs.existsSync(filename)) {
            throw new Error(`文件不存在: ${filename}`);
        }
        
        // 读取文件内容
        const content = fs.readFileSync(filename, 'utf8');
        
        // 基本语法检查
        validateSyntax(filename, content);
        
        // K6 特定检查
        validateK6Structure(filename, content);
        
        // RPC 配置检查
        validateRPCConfig(filename, content);
        
        console.log(`✅ ${filename} 验证通过`);
        validationResults.passed++;
        
    } catch (error) {
        console.error(`❌ ${filename} 验证失败: ${error.message}`);
        validationResults.failed++;
        validationResults.errors.push({
            file: filename,
            error: error.message
        });
    }
}

/**
 * 基本语法检查
 */
function validateSyntax(filename, content) {
    // 检查基本的 JavaScript 语法结构
    const requiredPatterns = [
        /import\s+.*\s+from\s+['"]k6\/http['"]/, // HTTP 导入
        /export\s+(let\s+)?options\s*=/, // 导出选项
        /export\s+default\s+function/, // 默认导出函数
    ];
    
    for (const pattern of requiredPatterns) {
        if (!pattern.test(content)) {
            throw new Error(`缺少必需的 K6 结构: ${pattern.source}`);
        }
    }
    
    // 检查是否有明显的语法错误
    const syntaxIssues = [
        { pattern: /\}\s*\{/, message: '可能的语法错误: 缺少分号或逗号' },
        { pattern: /\(\s*\)/, message: '空的函数参数' },
        { pattern: /console\.log\(\s*\)/, message: '空的 console.log' },
    ];
    
    for (const issue of syntaxIssues) {
        if (issue.pattern.test(content)) {
            console.warn(`⚠️  ${filename}: ${issue.message}`);
        }
    }
}

/**
 * K6 特定结构检查
 */
function validateK6Structure(filename, content) {
    // 检查 K6 特定的导入
    const k6Imports = [
        'k6/http',
        'k6/check'
    ];
    
    for (const importName of k6Imports) {
        if (!content.includes(importName)) {
            console.warn(`⚠️  ${filename}: 可能缺少导入 ${importName}`);
        }
    }
    
    // 检查是否有 check 函数调用
    if (!content.includes('check(')) {
        console.warn(`⚠️  ${filename}: 没有找到 check() 调用，可能缺少验证`);
    }
    
    // 检查是否有 HTTP 请求
    if (!content.includes('http.post(')) {
        throw new Error('没有找到 HTTP POST 请求');
    }
    
    // 检查选项配置
    if (content.includes('export let options') || content.includes('export const options')) {
        console.log(`   ✓ 找到 options 配置`);
    } else {
        console.warn(`⚠️  ${filename}: 没有找到 options 配置`);
    }
}

/**
 * RPC 配置检查
 */
function validateRPCConfig(filename, content) {
    // 检查 RPC URL
    const rpcUrlPatterns = [
        /https?:\/\/.*blockpi.*network/,
        /https?:\/\/.*arbitrum/,
        /RPC_URL|rpc_url|ARBITRUM_RPC_URL/
    ];
    
    let hasRpcUrl = false;
    for (const pattern of rpcUrlPatterns) {
        if (pattern.test(content)) {
            hasRpcUrl = true;
            break;
        }
    }
    
    if (!hasRpcUrl) {
        console.warn(`⚠️  ${filename}: 没有找到 Arbitrum RPC URL 配置`);
    }
    
    // 检查 RPC 方法
    const rpcMethods = [
        'eth_blockNumber',
        'eth_getBlockByNumber',
        'eth_getBlockByHash'
    ];
    
    for (const method of rpcMethods) {
        if (!content.includes(method)) {
            console.warn(`⚠️  ${filename}: 没有找到 RPC 方法 ${method}`);
        } else {
            console.log(`   ✓ 找到 RPC 方法: ${method}`);
        }
    }
    
    // 检查 JSON-RPC 结构
    if (!content.includes('jsonrpc')) {
        throw new Error('没有找到 JSON-RPC 结构');
    }
}

/**
 * 验证启动脚本
 */
function validateStartupScript() {
    console.log(`\n🔍 验证启动脚本: run_arbitrum_tests.sh`);
    
    try {
        if (!fs.existsSync('run_arbitrum_tests.sh')) {
            throw new Error('启动脚本不存在');
        }
        
        const content = fs.readFileSync('run_arbitrum_tests.sh', 'utf8');
        
        // 检查 shebang
        if (!content.startsWith('#!/bin/bash')) {
            console.warn('⚠️  启动脚本可能缺少正确的 shebang');
        }
        
        // 检查是否引用了所有测试脚本
        for (const scriptFile of SCRIPT_FILES) {
            if (!content.includes(scriptFile)) {
                console.warn(`⚠️  启动脚本没有引用 ${scriptFile}`);
            }
        }
        
        console.log('✅ 启动脚本验证通过');
        
    } catch (error) {
        console.error(`❌ 启动脚本验证失败: ${error.message}`);
        validationResults.errors.push({
            file: 'run_arbitrum_tests.sh',
            error: error.message
        });
    }
}

/**
 * 验证文档文件
 */
function validateDocumentation() {
    console.log(`\n🔍 验证文档: ARBITRUM_TEST_README.md`);
    
    try {
        if (!fs.existsSync('ARBITRUM_TEST_README.md')) {
            throw new Error('README 文档不存在');
        }
        
        const content = fs.readFileSync('ARBITRUM_TEST_README.md', 'utf8');
        
        // 检查是否包含所有脚本的说明
        for (const scriptFile of SCRIPT_FILES) {
            if (!content.includes(scriptFile)) {
                console.warn(`⚠️  README 没有包含 ${scriptFile} 的说明`);
            }
        }
        
        // 检查是否有使用示例
        if (!content.includes('k6 run')) {
            console.warn('⚠️  README 可能缺少使用示例');
        }
        
        console.log('✅ 文档验证通过');
        
    } catch (error) {
        console.error(`❌ 文档验证失败: ${error.message}`);
    }
}

/**
 * 生成验证报告
 */
function generateReport() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 验证报告');
    console.log('='.repeat(50));
    
    console.log(`✅ 通过: ${validationResults.passed} 个脚本`);
    console.log(`❌ 失败: ${validationResults.failed} 个脚本`);
    
    if (validationResults.errors.length > 0) {
        console.log('\n❌ 错误详情:');
        validationResults.errors.forEach((error, index) => {
            console.log(`${index + 1}. ${error.file}: ${error.error}`);
        });
    }
    
    if (validationResults.failed === 0) {
        console.log('\n🎉 所有脚本验证通过！可以开始测试了。');
        console.log('\n💡 快速开始:');
        console.log('   ./run_arbitrum_tests.sh quick');
        console.log('   或者');
        console.log('   k6 run arbitrum_issue_reproduce.js');
    } else {
        console.log('\n⚠️  请修复上述错误后再运行测试。');
    }
    
    console.log('\n' + '='.repeat(50));
}

/**
 * 主函数
 */
function main() {
    console.log('🚀 BlockPI Arbitrum RPC 测试脚本验证工具');
    console.log('==========================================');
    
    // 验证所有测试脚本
    for (const scriptFile of SCRIPT_FILES) {
        validateScript(scriptFile);
    }
    
    // 验证启动脚本
    validateStartupScript();
    
    // 验证文档
    validateDocumentation();
    
    // 生成报告
    generateReport();
}

// 运行验证
if (require.main === module) {
    main();
}

module.exports = {
    validateScript,
    validateSyntax,
    validateK6Structure,
    validateRPCConfig
};
