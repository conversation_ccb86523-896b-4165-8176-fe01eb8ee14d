# 🚀 BlockPI Arbitrum RPC 测试 - 快速开始

## 📋 概述

本测试套件专门用于测试和复现 BlockPI Arbitrum RPC 的问题。测试流程为：
1. `eth_blockNumber` - 获取最新区块号
2. `eth_getBlockByNumber` - 通过区块号获取区块信息
3. `eth_getBlockByHash` - 通过区块hash获取区块信息

## ⚡ 快速开始

### 1. 检查环境
```bash
# 检查是否安装了 K6
k6 version

# 如果没有安装，请安装 K6
# macOS:
brew install k6

# Ubuntu/Debian:
sudo apt update && sudo apt install k6
```

### 2. 运行测试

#### 方法一：使用启动脚本（推荐）
```bash
# 给脚本执行权限
chmod +x run_arbitrum_tests.sh

# 快速问题复现测试
./run_arbitrum_tests.sh quick

# 完整性能测试
./run_arbitrum_tests.sh full

# 高级多场景测试
./run_arbitrum_tests.sh advanced

# 查看帮助
./run_arbitrum_tests.sh --help
```

#### 方法二：直接运行 K6 脚本
```bash
# 简单问题复现
k6 run arbitrum_issue_reproduce.js

# 完整测试
k6 run arbitrum_blockpi_test.js

# 高级测试
k6 run arbitrum_advanced_test.js
```

## 📊 测试输出示例

### 正常输出
```
🔄 开始第 1 次测试...
📡 1. 调用 eth_blockNumber...
   ✅ 最新区块号: 0x15a2c8b (22,765,707)
📡 2. 调用 eth_getBlockByNumber(0x15a2c8b)...
   ✅ 区块hash: 0x1234567890abcdef...
   📊 区块信息: 交易数=156, gas=29,999,944
📡 3. 调用 eth_getBlockByHash(0x1234567890...)...
   ✅ 通过hash获取成功: 区块号=0x15a2c8b (22,765,707)
   📊 区块信息: 交易数=156, gas=29,999,944
✅ 第 1 次测试完成
```

### 问题输出
```
❌ eth_getBlockByNumber 失败: 500 {"error": "block not found"}
❌ 区块 0x15a2c8b 不存在
```

## 🔧 自定义配置

### 修改 RPC 端点
在脚本中找到并修改：
```javascript
const RPC_URL = 'https://your-custom-arbitrum-rpc.com';
```

### 调整测试参数
```bash
# 自定义虚拟用户数和持续时间
k6 run --vus 5 --duration 1m arbitrum_blockpi_test.js

# 输出结果到文件
k6 run --out json=results.json arbitrum_issue_reproduce.js
```

## 📁 文件说明

| 文件 | 用途 |
|------|------|
| `arbitrum_issue_reproduce.js` | 简单问题复现脚本 |
| `arbitrum_blockpi_test.js` | 完整性能测试脚本 |
| `arbitrum_advanced_test.js` | 高级多场景测试脚本 |
| `run_arbitrum_tests.sh` | 一键启动脚本 |
| `validate_scripts.js` | 脚本验证工具 |
| `ARBITRUM_TEST_README.md` | 详细使用文档 |

## 🐛 常见问题

### 1. K6 未安装
```bash
# 错误: k6: command not found
# 解决: 安装 K6
brew install k6  # macOS
```

### 2. 权限问题
```bash
# 错误: Permission denied
# 解决: 添加执行权限
chmod +x run_arbitrum_tests.sh
```

### 3. 网络超时
```bash
# 错误: request timeout
# 解决: 检查网络连接或降低并发数
k6 run --vus 1 arbitrum_issue_reproduce.js
```

### 4. RPC 限流
```bash
# 错误: rate limit exceeded
# 解决: 增加请求间隔或使用不同的 RPC 端点
```

## 📈 结果分析

### K6 指标说明
- `http_req_duration`: HTTP 请求响应时间
- `http_req_failed`: HTTP 请求失败率
- `http_reqs`: 每秒 HTTP 请求数
- `checks`: 验证检查通过率

### 性能基准
- ✅ 正常响应时间: < 3秒
- ✅ 失败率: < 10%
- ✅ 数据一致性: 100%

## 🎯 测试目标

1. **功能验证**: 确保三个 RPC 方法都能正常工作
2. **性能测试**: 验证响应时间在可接受范围内
3. **一致性检查**: 确保通过不同方式获取的区块数据一致
4. **错误处理**: 验证异常情况的处理

## 📞 获取帮助

1. **查看详细文档**: `cat ARBITRUM_TEST_README.md`
2. **验证脚本**: `node validate_scripts.js`
3. **查看脚本帮助**: `./run_arbitrum_tests.sh --help`

## 🔄 下一步

测试完成后，你可以：
1. 分析测试结果，识别性能瓶颈
2. 调整测试参数，进行更深入的测试
3. 根据发现的问题，联系 BlockPI 技术支持
4. 将测试集成到 CI/CD 流程中

---

**开始测试**: `./run_arbitrum_tests.sh quick` 🚀
