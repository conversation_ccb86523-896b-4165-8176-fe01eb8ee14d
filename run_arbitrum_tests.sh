#!/bin/bash

# BlockPI Arbitrum RPC 测试脚本启动器
# 使用方法: ./run_arbitrum_tests.sh [test_type] [options]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查 K6 是否安装
check_k6() {
    if ! command -v k6 &> /dev/null; then
        print_error "K6 未安装！"
        echo ""
        echo "请安装 K6:"
        echo "  macOS: brew install k6"
        echo "  Ubuntu/Debian: sudo apt update && sudo apt install k6"
        echo "  或访问: https://k6.io/docs/getting-started/installation/"
        exit 1
    fi
    
    local k6_version=$(k6 version | head -n1)
    print_success "K6 已安装: $k6_version"
}

# 显示帮助信息
show_help() {
    echo "BlockPI Arbitrum RPC 测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [test_type] [options]"
    echo ""
    echo "测试类型:"
    echo "  quick       - 快速问题复现测试 (默认)"
    echo "  full        - 完整性能测试"
    echo "  advanced    - 高级多场景测试"
    echo "  stress      - 压力测试"
    echo "  historical  - 历史区块测试"
    echo "  all         - 运行所有测试"
    echo ""
    echo "选项:"
    echo "  --vus N     - 设置虚拟用户数 (默认: 根据测试类型)"
    echo "  --duration T - 设置测试持续时间 (如: 30s, 2m)"
    echo "  --output F  - 输出结果到文件 (JSON格式)"
    echo "  --rpc URL   - 自定义 RPC 端点"
    echo "  --help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 quick"
    echo "  $0 full --vus 10 --duration 2m"
    echo "  $0 advanced --output results.json"
    echo "  $0 stress --rpc https://custom-arbitrum-rpc.com"
}

# 运行快速测试
run_quick_test() {
    print_info "运行快速问题复现测试..."
    local cmd="k6 run"
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        cmd="$cmd --out json=$OUTPUT_FILE"
    fi
    
    cmd="$cmd arbitrum_issue_reproduce.js"
    
    print_info "执行命令: $cmd"
    eval $cmd
}

# 运行完整测试
run_full_test() {
    print_info "运行完整性能测试..."
    local cmd="k6 run"
    
    if [[ -n "$VUS" ]]; then
        cmd="$cmd --vus $VUS"
    fi
    
    if [[ -n "$DURATION" ]]; then
        cmd="$cmd --duration $DURATION"
    fi
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        cmd="$cmd --out json=$OUTPUT_FILE"
    fi
    
    cmd="$cmd arbitrum_blockpi_test.js"
    
    print_info "执行命令: $cmd"
    eval $cmd
}

# 运行高级测试
run_advanced_test() {
    local scenario=${1:-""}
    print_info "运行高级多场景测试..."
    
    local cmd="k6 run"
    
    if [[ -n "$scenario" ]]; then
        cmd="$cmd -e K6_SCENARIO=$scenario"
    fi
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        cmd="$cmd --out json=$OUTPUT_FILE"
    fi
    
    cmd="$cmd arbitrum_advanced_test.js"
    
    print_info "执行命令: $cmd"
    eval $cmd
}

# 运行所有测试
run_all_tests() {
    print_info "运行所有测试套件..."
    
    echo ""
    print_info "=== 1/3: 快速问题复现测试 ==="
    run_quick_test
    
    echo ""
    print_info "=== 2/3: 完整性能测试 ==="
    run_full_test
    
    echo ""
    print_info "=== 3/3: 高级多场景测试 ==="
    run_advanced_test
    
    print_success "所有测试完成！"
}

# 检查测试文件是否存在
check_test_files() {
    local files=("arbitrum_issue_reproduce.js" "arbitrum_blockpi_test.js" "arbitrum_advanced_test.js")
    
    for file in "${files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "测试文件不存在: $file"
            print_info "请确保在正确的目录中运行此脚本"
            exit 1
        fi
    done
    
    print_success "所有测试文件检查通过"
}

# 主函数
main() {
    echo "🚀 BlockPI Arbitrum RPC 测试启动器"
    echo "=================================="
    
    # 检查依赖
    check_k6
    check_test_files
    
    # 解析参数
    TEST_TYPE="quick"
    VUS=""
    DURATION=""
    OUTPUT_FILE=""
    RPC_URL=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            quick|full|advanced|stress|historical|all)
                TEST_TYPE="$1"
                shift
                ;;
            --vus)
                VUS="$2"
                shift 2
                ;;
            --duration)
                DURATION="$2"
                shift 2
                ;;
            --output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            --rpc)
                RPC_URL="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示配置
    echo ""
    print_info "测试配置:"
    echo "  测试类型: $TEST_TYPE"
    [[ -n "$VUS" ]] && echo "  虚拟用户数: $VUS"
    [[ -n "$DURATION" ]] && echo "  持续时间: $DURATION"
    [[ -n "$OUTPUT_FILE" ]] && echo "  输出文件: $OUTPUT_FILE"
    [[ -n "$RPC_URL" ]] && echo "  RPC端点: $RPC_URL"
    echo ""
    
    # 运行测试
    case $TEST_TYPE in
        quick)
            run_quick_test
            ;;
        full)
            run_full_test
            ;;
        advanced)
            run_advanced_test
            ;;
        stress)
            run_advanced_test "stress_test"
            ;;
        historical)
            run_advanced_test "historical_blocks"
            ;;
        all)
            run_all_tests
            ;;
        *)
            print_error "未知测试类型: $TEST_TYPE"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    print_success "测试执行完成！"
    
    if [[ -n "$OUTPUT_FILE" ]]; then
        print_info "结果已保存到: $OUTPUT_FILE"
        print_info "可以使用以下命令查看结果:"
        echo "  cat $OUTPUT_FILE | jq ."
    fi
}

# 运行主函数
main "$@"
